"use client"
// Client-side rendered component
// app/profile/[id]/ClientProfilePage.tsx


import FullLayout from 'src/components/layouts/FullLayout'
import { useEffect, useState } from "react"
import { Button, Text, Loader, Group, SimpleGrid, Accordion, Stack, ActionIcon, Tooltip, Code } from "@mantine/core"
import { IconCoin, IconBrandTwitter, IconLink, IconBrandTelegram, IconBrandYoutube, IconBrandInstagram, IconBrandFacebook, IconBrandDiscord, IconBrandLinkedin, IconBrandTiktok, IconExternalLink, IconCopy, IconCheck, IconCurrencyBitcoin, IconCurrencyEthereum, IconCurrencySolana } from "@tabler/icons-react"
import { notifications } from '@mantine/notifications'
import { ContactCard } from "src/components/Card/contact"
import { useFooter } from "src/providers/FooterProvider"
import { AddToMobileContactButton } from "src/components/Buttons/AddToMobileContactButton"
import { getSocialData, getCryptoData, hasSocialData, hasCryptoData, getSocialUrl as utilGetSocialUrl } from "src/lib/database-utils"
import { SOCIAL_ICON_SIZE } from "src/lib/config"
import { AdSenseBanner } from "src/components/AdSense"
import { useDeviceDetection } from "src/hooks/useDeviceDetection"
import ImageGrid from "src/components/ImageGrid"

interface ContactItem {
  name: string;
  description: string | null;
  image: string | null;
  uri: string | null;
  profile: string | null;
  email: string | null;
  website: string | null;
  phone: string | null;
  tg_bot: string | null;
  notes: Record<string, string> | null;
  web2: string | null;
  web3: string | null;
  links: Record<string, string> | null;
  images: Record<string, string> | null;
  social: Record<string, string> | null;
  crypto: Record<string, string> | null;
  minted: string | null;
}

interface ClientProfilePageProps {
  contact: ContactItem;
  contactName: string;
}

export default function ClientProfilePage({ contact, contactName }: ClientProfilePageProps) {
  const [copiedAddress, setCopiedAddress] = useState<string | null>(null);
  const { setFooterContent, clearFooterContent } = useFooter();
  const { isMobile, isTablet, isDesktop } = useDeviceDetection();

  // Set footer content when contact is loaded - only on mobile/tablet devices
  useEffect(() => {
    if (contact && (isMobile || isTablet)) {
      setFooterContent(<AddToMobileContactButton contact={contact} />);
    } else {
      clearFooterContent();
    }
  }, [contact, isMobile, isTablet, setFooterContent, clearFooterContent]);

  // Clear footer content when component unmounts
  useEffect(() => {
    return () => {
      clearFooterContent();
    };
  }, [clearFooterContent]);

  // Helper function to copy wallet address
  const copyToClipboard = async (address: string, currency: string) => {
    try {
      await navigator.clipboard.writeText(address);
      setCopiedAddress(address);
      notifications.show({
        title: 'Copied!',
        message: `${currency} address copied to clipboard`,
        color: 'green',
      });
      setTimeout(() => {
        setCopiedAddress(null);
      }, 2000);
    } catch (err) {
      notifications.show({
        title: 'Error',
        message: 'Failed to copy address',
        color: 'red',
      });
    }
  };

  // Helper function to get crypto icon
  const getCryptoIcon = (currency: string) => {
    switch (currency.toLowerCase()) {
      case 'btc':
        return <IconCurrencyBitcoin size={16} />;
      case 'eth':
        return <IconCurrencyEthereum size={16} />;
      case 'sol':
        return <IconCurrencySolana size={16} />;
      default:
        return <IconCoin size={16} />;
    }
  };

  // Use the utility function for social media URLs
  const getSocialUrl = utilGetSocialUrl;

  // Helper functions to check if sections have any values using utility functions
  const hasCryptoValues = (contact: ContactItem): boolean => {
    return hasCryptoData(contact);
  };

  const hasSocialValues = (contact: ContactItem): boolean => {
    return hasSocialData(contact);
  };

  const hasLinksValues = (contact: ContactItem): boolean => {
    const hasCustomLinks = contact.links && Object.entries(contact.links).some(([key, value]) =>
      key.trim() && value.trim()
    );
    return !!(hasCustomLinks);
  };

  // Determine the default accordion value based on what sections have content
  const getDefaultAccordionValue = (contact: ContactItem): string => {
    if (hasSocialValues(contact)) return "social";
    if (hasCryptoValues(contact)) return "crypto";
    if (hasLinksValues(contact)) return "links";
    return "social"; // fallback to social
  };

  return (
    <FullLayout>
      <Stack gap="md">
        <SimpleGrid
          cols={{ base: 1, md: 2, lg: 2 }}
          spacing={{ base: 10, sm: 'xs' }}
          verticalSpacing={{ base: 'md', sm: 'xl' }}
        >
          <ContactCard name={contact.name} />

          {/* Show accordion only if there's at least one section with content */}
          {(hasCryptoValues(contact) || hasSocialValues(contact) || hasLinksValues(contact)) ? (
            <Accordion defaultValue={getDefaultAccordionValue(contact)} variant="separated">
              {/* Social Media Section - Only show if there are social values */}
              {hasSocialValues(contact) && (
                <Accordion.Item value="social">
                  <Accordion.Control icon={<IconBrandTwitter size={20} />}>
                    Social Media
                  </Accordion.Control>
                  <Accordion.Panel>
                    <Group justify="center" gap="lg">
                      {(() => {
                        const socialData = getSocialData(contact);
                        return Object.entries(socialData)
                          .filter(([platform, handle]) => handle && handle.trim())
                          .map(([platform, handle]) => {
                            const getSocialIcon = (platform: string) => {
                              switch (platform.toLowerCase()) {
                                case 'twitter':
                                  return <IconBrandTwitter size={30} />;
                                case 'telegram':
                                  return <IconBrandTelegram size={30} />;
                                case 'youtube':
                                  return <IconBrandYoutube size={30} />;
                                case 'instagram':
                                  return <IconBrandInstagram size={30} />;
                                case 'facebook':
                                  return <IconBrandFacebook size={30} />;
                                case 'discord':
                                  return <IconBrandDiscord size={30} />;
                                case 'linkedin':
                                  return <IconBrandLinkedin size={30} />;
                                case 'tiktok':
                                  return <IconBrandTiktok size={30} />;
                                default:
                                  return <IconExternalLink size={30} />;
                              }
                            };

                            const getSocialStyle = (platform: string) => {
                              switch (platform.toLowerCase()) {
                                case 'twitter':
                                  return { backgroundColor: '#1DA1F2', color: 'white' };
                                case 'telegram':
                                  return { backgroundColor: '#0088cc', color: 'white' };
                                case 'youtube':
                                  return { backgroundColor: '#FF0000', color: 'white' };
                                case 'instagram':
                                  return {
                                    background: 'linear-gradient(45deg, #f09433 0%,#e6683c 25%,#dc2743 50%,#cc2366 75%,#bc1888 100%)',
                                    color: 'white'
                                  };
                                case 'facebook':
                                  return { backgroundColor: '#1877F2', color: 'white' };
                                case 'discord':
                                  return { backgroundColor: '#5865F2', color: 'white' };
                                case 'linkedin':
                                  return { backgroundColor: '#0077B5', color: 'white' };
                                case 'tiktok':
                                  return { backgroundColor: '#000000', color: 'white' };
                                default:
                                  return { backgroundColor: '#868e96', color: 'white' };
                              }
                            };

                            return (
                              <Tooltip key={platform} label={`${platform}: ${handle}`} withArrow>
                                <ActionIcon
                                  component="a"
                                  href={getSocialUrl(platform, handle)}
                                  target="_blank"
                                  rel="noopener noreferrer"
                                  size={SOCIAL_ICON_SIZE}
                                  radius="xl"
                                  style={getSocialStyle(platform)}
                                >
                                  {getSocialIcon(platform)}
                                </ActionIcon>
                              </Tooltip>
                            );
                          });
                      })()}
                    </Group>
                  </Accordion.Panel>
                </Accordion.Item>
              )}

              {/* Links Section - Only show if there are link values */}
              {hasLinksValues(contact) && (
                <Accordion.Item value="links">
                  <Accordion.Control icon={<IconLink size={20} />}>
                    Links
                  </Accordion.Control>
                  <Accordion.Panel>
                    <Stack gap="sm">
                      {contact.links && Object.entries(contact.links)
                        .filter(([name, url]) => name.trim() && url.trim())
                        .map(([name, url]) => (
                          <Group key={name} justify="space-between">
                            <Button
                              leftSection={<IconExternalLink size={14} />}
                              variant="default"
                              size="xs"
                              component="a"
                              href={url.startsWith('http') ? url : `https://${url}`}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              {name}
                            </Button>
                          </Group>
                        ))}
                    </Stack>
                  </Accordion.Panel>
                </Accordion.Item>
              )}

              {/* Crypto Section - Only show if there are crypto values */}
              {hasCryptoValues(contact) && (
                <Accordion.Item value="crypto">
                  <Accordion.Control icon={<IconCoin size={20} />}>
                    Cryptocurrency
                  </Accordion.Control>
                  <Accordion.Panel>
                    <Stack gap="sm">
                      {(() => {
                        const cryptoData = getCryptoData(contact);
                        return Object.entries(cryptoData)
                          .filter(([currency, address]) => address && address.trim())
                          .map(([currency, address]) => (
                            <Group key={currency} justify="space-between" wrap="nowrap">
                              <Group gap="xs">
                                {getCryptoIcon(currency)}
                                <Text fw={500}>{currency.toUpperCase()}:</Text>
                              </Group>
                              <Group gap="xs" style={{ flex: 1, minWidth: 0 }}>
                                <Code style={{
                                  flex: 1,
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  border: '1px solid var(--mantine-color-gray-3)',
                                  padding: '4px 8px'
                                }}>
                                  {address}
                                </Code>
                                <Tooltip label={copiedAddress === address ? "Copied!" : "Copy address"}>
                                  <ActionIcon
                                    variant="light"
                                    size="sm"
                                    onClick={() => copyToClipboard(address, currency.toUpperCase())}
                                    color={copiedAddress === address ? "green" : "blue"}
                                  >
                                    {copiedAddress === address ? <IconCheck size={14} /> : <IconCopy size={14} />}
                                  </ActionIcon>
                                </Tooltip>
                              </Group>
                            </Group>
                          ));
                      })()}
                    </Stack>
                  </Accordion.Panel>
                </Accordion.Item>
              )}
              
              {contact.images && (
                <ImageGrid images={Object.values(contact.images)} />
              )}
            </Accordion>
          ) : (
            <div style={{ textAlign: 'center' }}>
              <Text ta="center" c="dimmed">
                No additional information available
              </Text>
              <AdSenseBanner slot="4503599449" responsive={false} width={250} height={250} />
            </div>
          )}
        </SimpleGrid>
      </Stack>
    </FullLayout>
  );
}
