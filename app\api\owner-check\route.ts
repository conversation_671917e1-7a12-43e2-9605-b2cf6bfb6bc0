import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';

// Rate limiting for security
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();
const RATE_LIMIT_WINDOW = 60 * 1000; // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10; // 10 requests per minute per IP

function checkRateLimit(ip: string): { allowed: boolean; resetTime?: number } {
  const now = Date.now();
  const userLimit = rateLimitMap.get(ip);

  if (!userLimit || now > userLimit.resetTime) {
    // Reset or create new limit
    rateLimitMap.set(ip, {
      count: 1,
      resetTime: now + RATE_LIMIT_WINDOW,
    });
    return { allowed: true };
  }

  if (userLimit.count >= RATE_LIMIT_MAX_REQUESTS) {
    return { allowed: false, resetTime: userLimit.resetTime };
  }

  userLimit.count++;
  return { allowed: true };
}

function sanitizeInput(input: string): string {
  if (!input || typeof input !== 'string') return '';
  // Remove potentially dangerous characters and limit length
  return input.trim().slice(0, 100).replace(/[<>'"&]/g, '');
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email) && email.length <= 254; // RFC 5321 limit
}

function validateContactName(name: string): boolean {
  // Contact name should be in format: name@primaryname
  const nameParts = name.split('@');
  if (nameParts.length !== 2) return false;
  
  const [username, primaryName] = nameParts;
  if (!username || !primaryName) return false;
  
  // Basic validation - alphanumeric and some special chars
  const validPattern = /^[a-zA-Z0-9._-]+$/;
  return validPattern.test(username) && validPattern.test(primaryName) && name.length <= 100;
}

export async function GET(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const ip = request.headers.get('x-forwarded-for') || 
               request.headers.get('x-real-ip') || 
               request.ip || 
               'unknown';

    // Check rate limit
    const rateLimit = checkRateLimit(ip);
    if (!rateLimit.allowed) {
      const resetTime = rateLimit.resetTime ? new Date(rateLimit.resetTime).toISOString() : 'unknown';
      return NextResponse.json(
        {
          error: 'Too many requests. Please try again later.',
          resetTime: resetTime
        },
        { status: 429 }
      );
    }

    // Get query parameters
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const name = searchParams.get('name');

    // Validate required parameters
    if (!email || !name) {
      return NextResponse.json(
        { error: 'Both email and name parameters are required' },
        { status: 400 }
      );
    }

    // Sanitize inputs
    const sanitizedEmail = sanitizeInput(email);
    const sanitizedName = sanitizeInput(name);

    // Validate email format
    if (!validateEmail(sanitizedEmail)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      );
    }

    // Validate contact name format
    if (!validateContactName(sanitizedName)) {
      return NextResponse.json(
        { error: 'Invalid contact name format. Expected format: name@primaryname' },
        { status: 400 }
      );
    }

    const supabase = getSupabaseClient();

    // Extract the TLN (Top Level Name) from the contact name
    const nameParts = sanitizedName.split('@');
    if (nameParts.length !== 2) {
      return NextResponse.json(
        { error: 'Invalid contact name format' },
        { status: 400 }
      );
    }

    const [, tln] = nameParts;

    // Check if the contact exists first
    const { data: contact, error: contactError } = await supabase
      .from('contact')
      .select('name')
      .eq('name', sanitizedName.toLowerCase())
      .single();

    if (contactError && contactError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking contact existence:', contactError);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // If contact doesn't exist, return false
    if (!contact) {
      return NextResponse.json({
        hasCreated: false,
        email: sanitizedEmail,
        name: sanitizedName
      });
    }

    // Check if the email is the owner of the TLN
    const { data: owner, error: ownerError } = await supabase
      .from('primary_name_owners')
      .select('id')
      .eq('user_email', sanitizedEmail.toLowerCase())
      .eq('owner_of', tln)
      .single();

    if (ownerError && ownerError.code !== 'PGRST116') { // PGRST116 is "not found" error
      console.error('Error checking TLN ownership:', ownerError);
      return NextResponse.json(
        { error: 'Internal server error' },
        { status: 500 }
      );
    }

    // Return true if the email owns the TLN and the contact exists
    const hasCreated = !!owner;

    return NextResponse.json({
      hasCreated,
      email: sanitizedEmail,
      name: sanitizedName
    });

  } catch (error) {
    console.error('Owner check API error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
