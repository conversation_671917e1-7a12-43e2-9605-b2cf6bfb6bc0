'use client';

import { useEffect, useState } from 'react';
import {
  Title,
  Text,
  Alert,
  LoadingOverlay,
  SimpleGrid,
  Paper,
  Group,
  ThemeIcon,
  Tabs,
  Stack,
  ActionIcon,
  Button,
} from '@mantine/core';
import {
  IconAlertCircle,
  IconUsers,
  IconAddressBook,
  IconBookmark,
  IconShield,
  IconDatabase,
  IconTrash,
  IconCoins,
} from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import { AdminStats, fetchAdminStats } from 'src/lib/admin';
import { ProfilesTable } from 'src/components/Admin/ProfilesTable';
import { ContactsTable } from 'src/components/Admin/ContactsTable';
import { AdminLayout } from 'src/components/layouts/AdminLayout';
import { checkAdminAccess, AdminAccessInfo } from 'src/lib/adminClient';

interface StatCardProps {
  title: string;
  value: number;
  icon: React.ReactNode;
  color: string;
}

interface BackupStatCardProps {
  title: string;
  lastBackupTime?: string;
  message?: string;
  icon: React.ReactNode;
  color: string;
  onDeleteBackups?: () => void;
}

function StatCard({ title, value, icon, color }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value.toLocaleString()}
          </Text>
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

function BackupStatCard({ title, lastBackupTime, message, icon, color, onDeleteBackups }: BackupStatCardProps) {
  const formatBackupTime = (timeString?: string) => {
    if (!timeString) return 'Never';
    const date = new Date(timeString);
    return date.toLocaleString();
  };

  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="apart">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="sm">
            {formatBackupTime(lastBackupTime)}
          </Text>
          {message && (
            <Text c="dimmed" fz="xs" mt={4}>
              {message}
            </Text>
          )}
          {onDeleteBackups && lastBackupTime && (
            <ActionIcon
              color="red"
              variant="light"
              size="sm"
              mt="xs"
              onClick={onDeleteBackups}
              title="Delete all backup files"
            >
              <IconTrash size={14} />
            </ActionIcon>
          )}
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

export default function AdminDashboard() {
  const [stats, setStats] = useState<AdminStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [adminAccess, setAdminAccess] = useState<AdminAccessInfo | null>(null);

  const handleDeleteBackups = () => {
    modals.openConfirmModal({
      title: 'Delete All Backup Files',
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to delete all backup files from the FTP server?
          <br /><br />
          <Text fw={500} c="red">
            ⚠️ This action cannot be undone and will permanently remove all backup files.
          </Text>
        </Text>
      ),
      labels: { confirm: 'Delete All Backups', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          const response = await fetch('/api/admin/delete-backups', {
            method: 'DELETE',
          });

          const result = await response.json();

          if (result.success) {
            notifications.show({
              title: 'Success',
              message: result.message,
              color: 'green',
            });

            // Refresh stats to update backup status
            const statsData = await fetchAdminStats();
            setStats(statsData);
          } else {
            notifications.show({
              title: 'Error',
              message: result.message || 'Failed to delete backup files',
              color: 'red',
            });
          }
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: 'Failed to delete backup files',
            color: 'red',
          });
        }
      },
    });
  };

  useEffect(() => {
    // Check admin access and fetch stats
    const fetchData = async () => {
      try {
        // First check admin access
        const accessInfo = await checkAdminAccess();
        setAdminAccess(accessInfo);

        if (!accessInfo.isAuthorized) {
          setError(accessInfo.error || 'Unauthorized access');
          return;
        }

        // Then fetch stats
        const statsData = await fetchAdminStats();
        setStats(statsData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <AdminLayout>
        <LoadingOverlay visible />
      </AdminLayout>
    );
  }

  if (error) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
          {error}
        </Alert>
      </AdminLayout>
    );
  }

  // Only allow super admin access to /admin
  if (adminAccess && !adminAccess.isSuperAdmin) {
    return (
      <AdminLayout>
        <Alert icon={<IconAlertCircle size={16} />} title="Access Denied" color="red">
          Super admin privileges required. Use /owner for namespace management.
        </Alert>
      </AdminLayout>
    );
  }

  // Show super admin dashboard
  return (
    <AdminLayout>
      <Stack gap="xl">
        {/* Header */}
        <Group justify="space-between">
          <Group>
            <ThemeIcon size={40} radius="md" color="red">
              <IconShield size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>Admin Dashboard</Title>
              <Text c="dimmed">Manage users, contacts, and system statistics</Text>
            </div>
          </Group>
          <Group>
            <Button
              variant="light"
              leftSection={<IconUsers size={16} />}
              onClick={() => window.location.href = '/admin/owner'}
            >
              Manage Owners
            </Button>
            <Button
              variant="light"
              leftSection={<IconCoins size={16} />}
              onClick={() => window.location.href = '/admin/points'}
            >
              Points Log
            </Button>
            <Button
              variant="light"
              leftSection={<IconDatabase size={16} />}
              onClick={() => window.location.href = '/admin/system-info'}
            >
              System Info
            </Button>
          </Group>
        </Group>

        {/* Statistics Cards */}
        {stats && (
          <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
            <StatCard
              title="Total Profiles"
              value={stats.profiles}
              icon={<IconUsers size={18} />}
              color="blue"
            />
            <StatCard
              title="Total Contacts"
              value={stats.contacts}
              icon={<IconAddressBook size={18} />}
              color="green"
            />
            <StatCard
              title="Total Bookmarks"
              value={stats.bookmarks}
              icon={<IconBookmark size={18} />}
              color="orange"
            />
            <BackupStatCard
              title="Last Backup"
              lastBackupTime={stats.lastBackupTime}
              message={stats.backupMessage}
              icon={<IconDatabase size={18} />}
              color="purple"
              onDeleteBackups={handleDeleteBackups}
            />
          </SimpleGrid>
        )}

        {/* Data Tables */}
        <Tabs defaultValue="profiles" variant="outline">
          <Tabs.List>
            <Tabs.Tab value="profiles" leftSection={<IconUsers size={16} />}>
              Profiles
            </Tabs.Tab>
            <Tabs.Tab value="contacts" leftSection={<IconAddressBook size={16} />}>
              Contacts
            </Tabs.Tab>
          </Tabs.List>

          <Tabs.Panel value="profiles" pt="md">
            <ProfilesTable />
          </Tabs.Panel>

          <Tabs.Panel value="contacts" pt="md">
            <ContactsTable />
          </Tabs.Panel>
        </Tabs>
      </Stack>
   </AdminLayout>
  );
}
