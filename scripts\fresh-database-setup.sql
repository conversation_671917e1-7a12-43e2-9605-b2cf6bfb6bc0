-- Fresh database setup script for ODude Names platform
-- This script creates all required tables including the new primary name ownership feature
--
-- Features included:
-- - Contact management with JSON structure
-- - Primary name ownership for scoped admin access
-- - Proper indexing for performance

-- Drop the existing contact table if it exists (for fresh start)
DROP TABLE IF EXISTS contact CASCADE;

-- Create the contact table with new structure
CREATE TABLE contact (
  timestamp TIMESTAMPTZ DEFAULT NOW(),
  name TEXT PRIMARY KEY,
  image TEXT,
  description TEXT,
  uri TEXT,
  profile TEXT,
  email TEXT,
  website TEXT,
  phone TEXT,
  tg_bot TEXT,
  notes JSONB,
  web2 TEXT,
  web3 TEXT,
  links JSON<PERSON>,
  images JSONB,
  social JSONB,
  crypto JSONB,
  extra JSONB,
  profile_email TEXT,
  minted TEXT
);

-- Create the primary_name_owners table for ownership management
CREATE TABLE primary_name_owners (
  id SERIAL PRIMARY KEY,
  user_email TEXT NOT NULL,
  owner_of TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX idx_primary_name_owners_user_email ON primary_name_owners(user_email);
CREATE INDEX idx_primary_name_owners_owner_of ON primary_name_owners(owner_of);

-- Add comments for documentation
COMMENT ON TABLE primary_name_owners IS 'Stores primary name ownership assignments for admin access control';
COMMENT ON COLUMN primary_name_owners.user_email IS 'Email of the user who owns the primary name';
COMMENT ON COLUMN primary_name_owners.owner_of IS 'The primary name (domain) that the user owns and can administrate';

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_contact_name ON contact (name);
CREATE INDEX IF NOT EXISTS idx_contact_profile_email ON contact (profile_email);
CREATE INDEX IF NOT EXISTS idx_contact_social ON contact USING GIN (social);
CREATE INDEX IF NOT EXISTS idx_contact_crypto ON contact USING GIN (crypto);
CREATE INDEX IF NOT EXISTS idx_contact_links ON contact USING GIN (links);
CREATE INDEX IF NOT EXISTS idx_contact_images ON contact USING GIN (images);
CREATE INDEX IF NOT EXISTS idx_contact_notes ON contact USING GIN (notes);
CREATE INDEX IF NOT EXISTS idx_contact_extra ON contact USING GIN (extra);

-- Add comments for documentation
COMMENT ON TABLE contact IS 'Contact information with JSON-based social media and crypto data';
COMMENT ON COLUMN contact.social IS 'JSON object containing social media handles: {"twitter": "handle", "telegram": "handle", etc.}';
COMMENT ON COLUMN contact.crypto IS 'JSON object containing cryptocurrency addresses: {"eth": "0x...", "btc": "1...", etc.}';
COMMENT ON COLUMN contact.links IS 'JSON object containing custom links: {"name": "url", etc.}';
COMMENT ON COLUMN contact.images IS 'JSON object containing image URLs: {"1": "url", "2": "url", etc.}';
COMMENT ON COLUMN contact.notes IS 'JSON object containing notes: {"title": "content", etc.}';
COMMENT ON COLUMN contact.extra IS 'JSON object containing extra data: {"map": "coordinates", etc.}';

-- Insert sample data to test the new structure
INSERT INTO contact (
  name,
  profile,
  description,
  email,
  website,
  phone,
  social,
  crypto,
  links,
  notes,
  extra,
  profile_email
) VALUES (
  'john.me',
  'John Doe',
  'Software Developer',
  '<EMAIL>',
  'https://johndoe.com',
  '+**********',
  '{"twitter": "johndoe", "telegram": "johndoe", "linkedin": "johndoe"}',
  '{"eth": "0x****************************************", "btc": "**********************************"}',
  '{"Portfolio": "https://portfolio.johndoe.com", "Blog": "https://blog.johndoe.com"}',
  '{"Bank Details": "Account: *********, Bank: Example Bank", "Address": "123 Main St, City, Country", "Emergency Contact": "Jane Doe: +**********"}',
  '{"map": "40.7128,-74.0060"}',
  '<EMAIL>'
);

-- Create settings table for user-specific configurations
CREATE TABLE IF NOT EXISTS settings (
  email TEXT PRIMARY KEY,
  max_contact_limit INTEGER DEFAULT 4,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add index for settings table
CREATE INDEX IF NOT EXISTS idx_settings_email ON settings (email);

-- Add comments for settings table
COMMENT ON TABLE settings IS 'User-specific settings and configurations';
COMMENT ON COLUMN settings.email IS 'User email address (primary key)';
COMMENT ON COLUMN settings.max_contact_limit IS 'Maximum number of contacts allowed for this user';
COMMENT ON COLUMN settings.created_at IS 'When the settings record was created';
COMMENT ON COLUMN settings.updated_at IS 'When the settings record was last updated';

-- Create trigger to update updated_at timestamp for settings
CREATE OR REPLACE FUNCTION update_settings_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_settings_updated_at
    BEFORE UPDATE ON settings
    FOR EACH ROW
    EXECUTE FUNCTION update_settings_updated_at();

-- Create user_points table to track user point balances
CREATE TABLE IF NOT EXISTS user_points (
  email TEXT PRIMARY KEY,
  points INTEGER DEFAULT 0 NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  CONSTRAINT points_non_negative CHECK (points >= 0)
);

-- Create transaction_logs table to track point transactions
CREATE TABLE IF NOT EXISTS transaction_logs (
  id SERIAL PRIMARY KEY,
  email TEXT NOT NULL,
  transaction_type TEXT NOT NULL,
  points_change INTEGER NOT NULL,
  points_before INTEGER NOT NULL,
  points_after INTEGER NOT NULL,
  description TEXT,
  reference_id TEXT,
  from_email TEXT,
  to_email TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_points_email ON user_points (email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_email ON transaction_logs (email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_type ON transaction_logs (transaction_type);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_created_at ON transaction_logs (created_at);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_reference_id ON transaction_logs (reference_id);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_from_email ON transaction_logs (from_email);
CREATE INDEX IF NOT EXISTS idx_transaction_logs_to_email ON transaction_logs (to_email);

-- Add foreign key constraint to link transaction_logs with user_points
ALTER TABLE transaction_logs
ADD CONSTRAINT fk_transaction_logs_email
FOREIGN KEY (email) REFERENCES user_points(email) ON DELETE CASCADE;

-- Add comments for documentation
COMMENT ON TABLE user_points IS 'Stores user point balances for the reward system';
COMMENT ON COLUMN user_points.email IS 'User email address (primary key)';
COMMENT ON COLUMN user_points.points IS 'Current point balance for the user';
COMMENT ON COLUMN user_points.created_at IS 'When the user points record was created';
COMMENT ON COLUMN user_points.updated_at IS 'When the user points record was last updated';

COMMENT ON TABLE transaction_logs IS 'Logs all point transactions for audit trail';
COMMENT ON COLUMN transaction_logs.email IS 'User email address';
COMMENT ON COLUMN transaction_logs.transaction_type IS 'Type of transaction (SIGNUP, CREATE_CONTACT, BOOKMARK_ADD, BOOKMARK_REMOVE, TRANSFER_SEND, TRANSFER_RECEIVE)';
COMMENT ON COLUMN transaction_logs.points_change IS 'Points added (positive) or deducted (negative)';
COMMENT ON COLUMN transaction_logs.points_before IS 'Point balance before transaction';
COMMENT ON COLUMN transaction_logs.points_after IS 'Point balance after transaction';
COMMENT ON COLUMN transaction_logs.description IS 'Human readable description of the transaction';
COMMENT ON COLUMN transaction_logs.reference_id IS 'Reference to related entity (contact name, bookmark id, etc.)';
COMMENT ON COLUMN transaction_logs.from_email IS 'Email of user sending points (for transfers)';
COMMENT ON COLUMN transaction_logs.to_email IS 'Email of user receiving points (for transfers)';
COMMENT ON COLUMN transaction_logs.created_at IS 'When the transaction occurred';

-- Create trigger to update updated_at timestamp for user_points
CREATE OR REPLACE FUNCTION update_user_points_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_user_points_updated_at
    BEFORE UPDATE ON user_points
    FOR EACH ROW
    EXECUTE FUNCTION update_user_points_updated_at();

-- Create function to safely add/subtract points with transaction logging
CREATE OR REPLACE FUNCTION update_user_points(
    user_email TEXT,
    points_change INTEGER,
    transaction_type TEXT,
    description TEXT DEFAULT NULL,
    reference_id TEXT DEFAULT NULL,
    from_email TEXT DEFAULT NULL,
    to_email TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    current_points INTEGER;
    new_points INTEGER;
BEGIN
    -- Get current points or create user if doesn't exist
    INSERT INTO user_points (email, points)
    VALUES (user_email, 0)
    ON CONFLICT (email) DO NOTHING;

    SELECT points INTO current_points
    FROM user_points
    WHERE email = user_email;

    -- Calculate new points
    new_points := current_points + points_change;

    -- Check if user has enough points for deduction
    IF new_points < 0 THEN
        RETURN FALSE;
    END IF;

    -- Update user points
    UPDATE user_points
    SET points = new_points
    WHERE email = user_email;

    -- Log the transaction
    INSERT INTO transaction_logs (
        email,
        transaction_type,
        points_change,
        points_before,
        points_after,
        description,
        reference_id,
        from_email,
        to_email
    ) VALUES (
        user_email,
        transaction_type,
        points_change,
        current_points,
        new_points,
        description,
        reference_id,
        from_email,
        to_email
    );

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Create a function specifically for point transfers
CREATE OR REPLACE FUNCTION transfer_points(
    sender_email TEXT,
    recipient_email TEXT,
    points_amount INTEGER,
    description TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
    sender_current_points INTEGER;
    transfer_id TEXT;
BEGIN
    -- Generate a unique transfer ID
    transfer_id := 'TRANSFER_' || extract(epoch from now())::text || '_' || floor(random() * 1000)::text;

    -- Check if sender has enough points
    SELECT points INTO sender_current_points
    FROM user_points
    WHERE email = sender_email;

    IF sender_current_points IS NULL OR sender_current_points < points_amount THEN
        RETURN FALSE;
    END IF;

    -- Check if recipient exists in user_points (has a profile)
    IF NOT EXISTS (SELECT 1 FROM user_points WHERE email = recipient_email) THEN
        RETURN FALSE;
    END IF;

    -- Deduct points from sender
    IF NOT update_user_points(
        sender_email,
        -points_amount,
        'TRANSFER_SEND',
        COALESCE(description, 'Points transferred to ' || recipient_email),
        transfer_id,
        sender_email,
        recipient_email
    ) THEN
        RETURN FALSE;
    END IF;

    -- Add points to recipient
    IF NOT update_user_points(
        recipient_email,
        points_amount,
        'TRANSFER_RECEIVE',
        COALESCE(description, 'Points received from ' || sender_email),
        transfer_id,
        sender_email,
        recipient_email
    ) THEN
        -- Rollback sender transaction if recipient transaction fails
        PERFORM update_user_points(
            sender_email,
            points_amount,
            'TRANSFER_ROLLBACK',
            'Transfer failed - points restored',
            transfer_id
        );
        RETURN FALSE;
    END IF;

    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;

-- Verification queries
SELECT 'Contact, settings, and point system tables created successfully' as status;
SELECT COUNT(*) as total_contacts FROM contact;
SELECT COUNT(*) as total_user_points FROM user_points;
SELECT COUNT(*) as total_transaction_logs FROM transaction_logs;
SELECT name, social, crypto, notes FROM contact WHERE social IS NOT NULL OR crypto IS NOT NULL OR notes IS NOT NULL;
