import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function GET(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = (page - 1) * limit;

    const supabase = getSupabaseClient();

    // Get profiles with contact count
    const { data: profiles, error: profilesError } = await supabase
      .from('profiles')
      .select(`
        id,
        created_at,
        updated_at,
        full_name,
        email,
        avatar_url,
        disabled
      `)
      .order('updated_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (profilesError) {
      console.error('Error fetching profiles:', profilesError);
      return NextResponse.json({ error: 'Failed to fetch profiles' }, { status: 500 });
    }

    // Get contact counts for each profile
    const profilesWithCounts = await Promise.all(
      (profiles || []).map(async (profile) => {
        let contactCountQuery = supabase
          .from('contact')
          .select('*', { count: 'exact', head: true })
          .eq('profile_email', profile.email);

        // For owners, only count contacts in their namespace
        if (!authResult.isSuperAdmin && authResult.isOwner) {
          // Build OR condition for owned primary names
          const namespaceConditions = authResult.ownedPrimaryNames.map(primaryName =>
            `name.like.%@${primaryName}`
          ).join(',');

          if (namespaceConditions) {
            contactCountQuery = contactCountQuery.or(namespaceConditions);
          } else {
            // If no valid namespace conditions, return 0 count
            return {
              ...profile,
              contact_count: 0,
            };
          }
        }

        const { count: contactCount } = await contactCountQuery;

        return {
          ...profile,
          contact_count: contactCount || 0,
        };
      })
    );

    // Filter out profiles with 0 contacts for owners (they shouldn't see profiles without contacts in their namespace)
    let finalProfiles = profilesWithCounts;
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      finalProfiles = profilesWithCounts.filter(profile => profile.contact_count > 0);
    }

    // Calculate total count based on access level
    let totalCount = 0;
    if (authResult.isSuperAdmin) {
      const { count, error: countError } = await supabase
        .from('profiles')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        console.error('Error fetching profiles count:', countError);
        return NextResponse.json({ error: 'Failed to fetch profiles count' }, { status: 500 });
      }
      totalCount = count || 0;
    } else {
      // For owners, use the filtered profiles count
      totalCount = finalProfiles.length;
    }

    return NextResponse.json({
      data: finalProfiles,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
      },
    });

  } catch (error) {
    console.error('Admin profiles API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    // Verify admin authentication
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    // Only super admin can delete profiles
    if (!authResult.isSuperAdmin) {
      return NextResponse.json({ error: 'Unauthorized - only super admin can delete profiles' }, { status: 403 });
    }

    const { profileEmail } = await request.json();

    if (!profileEmail) {
      return NextResponse.json({ error: 'Profile email is required' }, { status: 400 });
    }

    const supabase = getSupabaseClient();

    // Delete in order to handle foreign key constraints
    // 1. Delete transaction logs
    const { error: transactionError } = await supabase
      .from('transaction_logs')
      .delete()
      .eq('email', profileEmail);

    if (transactionError) {
      console.error('Error deleting transaction logs:', transactionError);
    }

    // 2. Delete user points
    const { error: pointsError } = await supabase
      .from('user_points')
      .delete()
      .eq('email', profileEmail);

    if (pointsError) {
      console.error('Error deleting user points:', pointsError);
    }

    // 3. Delete bookmarks made by this user
    const { error: bookmarkError } = await supabase
      .from('bookmark')
      .delete()
      .eq('contact_email', profileEmail);

    if (bookmarkError) {
      console.error('Error deleting bookmarks:', bookmarkError);
    }

    // 4. Delete contacts created by this user
    const { error: contactError } = await supabase
      .from('contact')
      .delete()
      .eq('profile_email', profileEmail);

    if (contactError) {
      console.error('Error deleting contacts:', contactError);
    }

    // 5. Delete user settings
    const { error: settingsError } = await supabase
      .from('settings')
      .delete()
      .eq('email', profileEmail);

    if (settingsError) {
      console.error('Error deleting settings:', settingsError);
    }

    // 6. Finally, delete the profile
    const { error: profileError } = await supabase
      .from('profiles')
      .delete()
      .eq('email', profileEmail);

    if (profileError) {
      console.error('Error deleting profile:', profileError);
      return NextResponse.json({ error: 'Failed to delete profile' }, { status: 500 });
    }

    return NextResponse.json({ success: true, message: 'Profile deleted successfully' });

  } catch (error) {
    console.error('Admin delete profile API error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
