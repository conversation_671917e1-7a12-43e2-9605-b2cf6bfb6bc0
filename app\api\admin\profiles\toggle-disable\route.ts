import { NextRequest, NextResponse } from 'next/server';
import { getSupabaseClient } from 'src/lib/supabase';
import { verifyAdminAuth } from 'src/lib/adminAuth';

export async function POST(request: NextRequest) {
  try {
    // Verify admin authentication (allows both super admin and owners)
    const authResult = await verifyAdminAuth(request);
    if (!authResult.isAuthorized) {
      return NextResponse.json({ error: authResult.error }, { status: authResult.status });
    }

    const { email } = await request.json();

    if (!email) {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // For profile operations, we need to check if the profile has contacts in the owner's namespace
    if (!authResult.isSuperAdmin && authResult.isOwner) {
      const supabase = getSupabaseClient();

      // Check if this profile has any contacts in the owner's namespace
      const namespaceConditions = authResult.ownedPrimaryNames.map(primaryName =>
        `name.like.%@${primaryName}`
      ).join(',');

      const { data: contacts, error: contactsError } = await supabase
        .from('contact')
        .select('name')
        .eq('profile_email', email)
        .or(namespaceConditions);

      if (contactsError) {
        console.error('Error checking profile contacts:', contactsError);
        return NextResponse.json({ error: 'Failed to verify profile permissions' }, { status: 500 });
      }

      // If no contacts in owner's namespace, deny access
      if (!contacts || contacts.length === 0) {
        return NextResponse.json({ error: 'Unauthorized - profile not in your namespace' }, { status: 403 });
      }
    }

    const supabase = getSupabaseClient();

    // Get current disabled status
    const { data: profile, error: fetchError } = await supabase
      .from('profiles')
      .select('disabled')
      .eq('email', email)
      .single();

    if (fetchError) {
      console.error('Error fetching profile:', fetchError);
      return NextResponse.json({ error: 'Profile not found' }, { status: 404 });
    }

    // Toggle the disabled status
    const newDisabledStatus = !profile.disabled;

    const { error: updateError } = await supabase
      .from('profiles')
      .update({ disabled: newDisabledStatus })
      .eq('email', email);

    if (updateError) {
      console.error('Error updating profile:', updateError);
      return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
    }

    return NextResponse.json({
      message: `Profile ${newDisabledStatus ? 'disabled' : 'enabled'} successfully`,
      disabled: newDisabledStatus
    });

  } catch (error) {
    console.error('Toggle profile disable error:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
