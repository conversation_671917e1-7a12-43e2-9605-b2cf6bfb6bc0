'use client';

import { useState, useEffect } from 'react';
import {
  Table,
  Text,
  Pagination,
  LoadingOverlay,
  Alert,
  Avatar,
  Group,
  Badge,
  Card,
  TextInput,
  Button,
  ActionIcon,
  Stack,
  Select,
  ScrollArea,
  Anchor,
} from '@mantine/core';
import { IconAlertCircle, IconSearch, IconTrash, IconPhotoOff, IconBan, IconCheck } from '@tabler/icons-react';
import { modals } from '@mantine/modals';
import { notifications } from '@mantine/notifications';
import {
  ContactData,
  fetchContactsData,
  deleteContact,
  clearImageStatus,
  formatTimestamp,
  getProfileDisplayName
} from '../../lib/admin';
import { useDebouncedValue } from '@mantine/hooks';
import { useSession } from 'next-auth/react';
import { canPerformContactActions } from '../../lib/ownershipUtils';

interface ContactsTableProps {
  showActions?: boolean; // Controls whether to show action buttons
  checkOwnership?: boolean; // Controls whether to check ownership for individual contacts
}

export function ContactsTable({ showActions = true, checkOwnership = false }: ContactsTableProps) {
  const { data: session } = useSession();
  const [contacts, setContacts] = useState<ContactData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [total, setTotal] = useState(0);
  const [contactActionPermissions, setContactActionPermissions] = useState<Map<string, boolean>>(new Map());
  const [searchQuery, setSearchQuery] = useState('');
  const [profileFilter, setProfileFilter] = useState('');
  const [imageFilter, setImageFilter] = useState('');
  const [mintedFilter, setMintedFilter] = useState('');
  const [debouncedSearch] = useDebouncedValue(searchQuery, 500);

  const ITEMS_PER_PAGE = 50;

  const fetchData = async (page: number, search: string = '', profile: string = '', image: string = '', minted: string = '') => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetchContactsData(page, ITEMS_PER_PAGE, search, profile, image, minted);

      setContacts(response.data);
      if (response.pagination) {
        setTotalPages(response.pagination.totalPages);
        setTotal(response.pagination.total);
      }

      // Check permissions for each contact if ownership checking is enabled
      if (checkOwnership && session?.user?.email) {
        const permissionsMap = new Map<string, boolean>();

        for (const contact of response.data) {
          try {
            const canPerform = await canPerformContactActions(
              session.user.email,
              contact.name,
              false // Assuming non-super admin for owner page
            );
            permissionsMap.set(contact.name, canPerform);
          } catch (error) {
            console.error(`Error checking permissions for ${contact.name}:`, error);
            permissionsMap.set(contact.name, false);
          }
        }

        setContactActionPermissions(permissionsMap);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch contacts');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData(currentPage, debouncedSearch, profileFilter, imageFilter, mintedFilter);
  }, [currentPage, debouncedSearch, profileFilter, imageFilter, mintedFilter]);

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleSearch = () => {
    setCurrentPage(1);
    fetchData(1, searchQuery, profileFilter, imageFilter, mintedFilter);
  };

  const handleClearFilters = () => {
    setSearchQuery('');
    setProfileFilter('');
    setImageFilter('');
    setMintedFilter('');
    setCurrentPage(1);
  };

  const handleDeleteContact = async (contactName: string) => {
    modals.openConfirmModal({
      title: 'Delete Contact',
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to delete "{contactName}"? This action will permanently remove:
          <br />• The contact record
          <br />• All bookmarks for this contact
          <br />• All uploaded images
          <br /><br />
          <Text fw={500} c="orange">
            ⚠️ Important: Points spent to create this contact (1000 points) will NOT be refunded.
          </Text>
          <br />
          This action cannot be undone.
        </Text>
      ),
      labels: { confirm: 'Delete', cancel: 'Cancel' },
      confirmProps: { color: 'red' },
      onConfirm: async () => {
        try {
          await deleteContact(contactName);

          notifications.show({
            title: 'Success',
            message: 'Contact deleted successfully',
            color: 'green',
          });

          // Refresh the data
          fetchData(currentPage, debouncedSearch, profileFilter, imageFilter, mintedFilter);
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: error instanceof Error ? error.message : 'Failed to delete contact',
            color: 'red',
          });
        }
      },
    });
  };

  const handleClearImageStatus = async (contactName: string) => {
    modals.openConfirmModal({
      title: 'Clear Image Status',
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to clear the image status for "{contactName}"?
          This will change the Image status from "Yes" to "No", allowing the user to apply for QR again.
        </Text>
      ),
      labels: { confirm: 'Clear Image', cancel: 'Cancel' },
      confirmProps: { color: 'orange' },
      onConfirm: async () => {
        try {
          await clearImageStatus(contactName);

          notifications.show({
            title: 'Success',
            message: 'Image status cleared successfully',
            color: 'green',
          });

          // Refresh the data
          fetchData(currentPage, debouncedSearch, profileFilter, imageFilter, mintedFilter);
        } catch (error) {
          notifications.show({
            title: 'Error',
            message: error instanceof Error ? error.message : 'Failed to clear image status',
            color: 'red',
          });
        }
      },
    });
  };

  const handleToggleDisable = async (contactName: string, currentDisabled: boolean) => {
    const action = currentDisabled ? 'enable' : 'disable';

    modals.openConfirmModal({
      title: `${action.charAt(0).toUpperCase() + action.slice(1)} Contact`,
      centered: true,
      children: (
        <Text size="sm">
          Are you sure you want to {action} the contact "{contactName}"?
          {!currentDisabled && ' Disabled contacts will show a temporary disabled message on their profile page.'}
        </Text>
      ),
      labels: { confirm: action.charAt(0).toUpperCase() + action.slice(1), cancel: 'Cancel' },
      confirmProps: { color: currentDisabled ? 'green' : 'orange' },
      onConfirm: async () => {
        try {
          const response = await fetch('/api/admin/contacts/toggle-disable', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ name: contactName }),
          });

          if (response.ok) {
            const result = await response.json();
            notifications.show({
              title: 'Contact Updated',
              message: result.message,
              color: 'green',
            });
            fetchData(currentPage, debouncedSearch, profileFilter, imageFilter, mintedFilter);
          } else {
            throw new Error('Failed to toggle contact status');
          }
        } catch (error) {
          console.error('Error toggling contact status:', error);
          notifications.show({
            title: 'Error',
            message: 'Failed to update contact status. Please try again.',
            color: 'red',
          });
        }
      },
    });
  };

  if (error) {
    return (
      <Alert icon={<IconAlertCircle size={16} />} title="Error" color="red">
        {error}
      </Alert>
    );
  }

  const rows = contacts.map((contact) => {
    const canPerformActions = checkOwnership
      ? contactActionPermissions.get(contact.name) ?? false
      : true;
    const shouldShowActions = showActions && (!checkOwnership || canPerformActions);

    return (
      <Table.Tr key={contact.name}>
        <Table.Td>
          <Text size="sm">{formatTimestamp(contact.timestamp)}</Text>
        </Table.Td>
        <Table.Td>
          <Group gap="sm">
            <Avatar src={contact.image} size={30} radius="xl" />
            <Anchor
              href={`/profile/${contact.name}`}
              target="_blank"
              rel="noopener noreferrer"
              size="sm"
              fw={500}
              style={{ textDecoration: 'none' }}
            >
              {contact.name}
            </Anchor>
          </Group>
        </Table.Td>
        <Table.Td>
          <Text size="sm">
            {getProfileDisplayName(contact.profile, contact.profile_email)}
          </Text>
        </Table.Td>
        <Table.Td>
          <Badge color={contact.image ? 'green' : 'gray'} variant="light">
            {contact.image ? 'Yes' : 'No'}
          </Badge>
        </Table.Td>
        <Table.Td>
          <Badge color={contact.minted ? 'blue' : 'gray'} variant="light">
            {contact.minted ? 'Yes' : 'No'}
          </Badge>
        </Table.Td>
        <Table.Td>
          <Badge color={contact.disabled ? 'red' : 'green'} variant="light">
            {contact.disabled ? 'Disabled' : 'Active'}
          </Badge>
        </Table.Td>
        {shouldShowActions && (
          <Table.Td>
            <Group gap="xs">
              <ActionIcon
                color={contact.disabled ? 'green' : 'orange'}
                variant="light"
                onClick={() => handleToggleDisable(contact.name, contact.disabled)}
                title={contact.disabled ? 'Enable Contact' : 'Disable Contact'}
              >
                {contact.disabled ? <IconCheck size={16} /> : <IconBan size={16} />}
              </ActionIcon>
              {contact.image && (
                <ActionIcon
                  color="orange"
                  variant="light"
                  onClick={() => handleClearImageStatus(contact.name)}
                  title="Clear Image Status"
                >
                  <IconPhotoOff size={16} />
                </ActionIcon>
              )}
              <ActionIcon
                color="red"
                variant="light"
                onClick={() => handleDeleteContact(contact.name)}
                title="Delete Contact"
              >
                <IconTrash size={16} />
              </ActionIcon>
            </Group>
          </Table.Td>
        )}
      </Table.Tr>
    );
  });

  return (
    <Card withBorder>
      <div style={{ position: 'relative' }}>
        <LoadingOverlay visible={loading} />
        
        <Text size="lg" fw={500} mb="md">
          Contacts ({total} total)
        </Text>

        <Stack gap="md" mb="md">
          <Group>
            <TextInput
              placeholder="Search contacts..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="Image Status"
              value={imageFilter}
              onChange={(value) => setImageFilter(value || '')}
              data={[
                { value: '', label: 'All Images' },
                { value: 'yes', label: 'Has Image' },
                { value: 'no', label: 'No Image' },
              ]}
              clearable
              style={{ minWidth: 140 }}
            />
            <Select
              placeholder="Minted Status"
              value={mintedFilter}
              onChange={(value) => setMintedFilter(value || '')}
              data={[
                { value: '', label: 'All Minted' },
                { value: 'yes', label: 'Minted' },
                { value: 'no', label: 'Not Minted' },
              ]}
              clearable
              style={{ minWidth: 140 }}
            />
            <Button onClick={handleSearch} variant="light">
              Search
            </Button>
            <Button onClick={handleClearFilters} variant="subtle">
              Clear
            </Button>
          </Group>
        </Stack>

        <ScrollArea>
          <Table striped highlightOnHover>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Created</Table.Th>
                <Table.Th>Name</Table.Th>
                <Table.Th>Profile</Table.Th>
                <Table.Th>Image</Table.Th>
                <Table.Th>Minted</Table.Th>
                <Table.Th>Status</Table.Th>
                {showActions && <Table.Th>Actions</Table.Th>}
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {rows.length > 0 ? (
                rows
              ) : (
                <Table.Tr>
                  <Table.Td colSpan={showActions ? 7 : 6}>
                    <Text ta="center" c="dimmed">
                      No contacts found
                    </Text>
                  </Table.Td>
                </Table.Tr>
              )}
            </Table.Tbody>
          </Table>
        </ScrollArea>

        {totalPages > 1 && (
          <Group justify="center" mt="md">
            <Pagination
              value={currentPage}
              onChange={handlePageChange}
              total={totalPages}
              size="sm"
            />
          </Group>
        )}
      </div>
    </Card>
  );
}
