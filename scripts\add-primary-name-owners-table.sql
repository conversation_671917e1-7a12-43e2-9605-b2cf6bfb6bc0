-- Add primary_name_owners table for ownership management
-- This script adds the primary name ownership functionality to existing databases

-- Create the primary_name_owners table
CREATE TABLE IF NOT EXISTS primary_name_owners (
  id SERIAL PRIMARY KEY,
  user_email TEXT NOT NULL,
  owner_of TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for efficient querying
CREATE INDEX IF NOT EXISTS idx_primary_name_owners_user_email ON primary_name_owners(user_email);
CREATE INDEX IF NOT EXISTS idx_primary_name_owners_owner_of ON primary_name_owners(owner_of);

-- Add comments for documentation
COMMENT ON TABLE primary_name_owners IS 'Stores primary name ownership assignments for admin access control';
COMMENT ON COLUMN primary_name_owners.user_email IS 'Email of the user who owns the primary name';
COMMENT ON COLUMN primary_name_owners.owner_of IS 'The primary name (domain) that the user owns and can administrate';

-- Example data (commented out - uncomment and modify as needed)
-- INSERT INTO primary_name_owners (user_email, owner_of) VALUES 
-- ('<EMAIL>', 'shop'),
-- ('<EMAIL>', 'me'),
-- ('<EMAIL>', 'info');

-- Verify the table was created successfully
SELECT 'primary_name_owners table created successfully' AS status;
